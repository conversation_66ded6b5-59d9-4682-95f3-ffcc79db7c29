/**
 * خدمة التعامل مع الوقت والتاريخ في التطبيق
 * هذه الخدمة تقدم وظائف للتعامل مع الوقت والتاريخ بشكل موحد في جميع أنحاء التطبيق
 * مع مراعاة المنطقة الزمنية لطرابلس، ليبيا (UTC+2)
 * تدعم الآن إعدادات التنسيق المخصصة من قاعدة البيانات
 *
 * هذا الملف يجب أن يكون متزامنًا مع ملف datetime_utils.py في الخادم الخلفي
 * لضمان اتساق معالجة التوقيت في جميع أنحاء التطبيق.
 */

import api from '../lib/axios';

// المنطقة الزمنية لطرابلس، ليبيا
export const TRIPOLI_TIMEZONE = 'Africa/Tripoli'; // UTC+2
export const TRIPOLI_TIMEZONE_OFFSET = 2; // ساعات

// واجهة إعدادات التاريخ والوقت
export interface DateTimeSettings {
  dateFormat: string;
  timeFormat: string;
  timezone: string;
  dateLanguage: string;
  weekStartDay: string;
  dateSeparator: string;
  timeSeparator: string;
  showSeconds: boolean;
  autoDetectTimezone: boolean;
  datetimeDisplayFormat: string;
}

// الإعدادات الافتراضية
const DEFAULT_SETTINGS: DateTimeSettings = {
  dateFormat: 'dd/MM/yyyy',
  timeFormat: '24h',
  timezone: 'Africa/Tripoli',
  dateLanguage: 'ar',
  weekStartDay: 'saturday',
  dateSeparator: '/',
  timeSeparator: ':',
  showSeconds: false,
  autoDetectTimezone: false,
  datetimeDisplayFormat: 'separate'
};

// كاش للإعدادات
let cachedSettings: DateTimeSettings | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 دقائق

/**
 * جلب إعدادات التاريخ والوقت من الخادم
 */
export const fetchDateTimeSettings = async (): Promise<DateTimeSettings> => {
  const now = Date.now();

  // استخدام الكاش إذا كان متاحاً وحديثاً
  if (cachedSettings && (now - lastFetchTime) < CACHE_DURATION) {
    return cachedSettings;
  }

  try {
    const response = await api.get('/api/settings');
    const settingsData = response.data.reduce((acc: Record<string, string>, curr: any) => {
      acc[curr.key] = curr.value;
      return acc;
    }, {});

    const settings: DateTimeSettings = {
      dateFormat: settingsData.date_format || DEFAULT_SETTINGS.dateFormat,
      timeFormat: settingsData.time_format || DEFAULT_SETTINGS.timeFormat,
      timezone: settingsData.timezone || DEFAULT_SETTINGS.timezone,
      dateLanguage: settingsData.date_language || DEFAULT_SETTINGS.dateLanguage,
      weekStartDay: settingsData.week_start_day || DEFAULT_SETTINGS.weekStartDay,
      dateSeparator: settingsData.date_separator || DEFAULT_SETTINGS.dateSeparator,
      timeSeparator: settingsData.time_separator || DEFAULT_SETTINGS.timeSeparator,
      showSeconds: settingsData.show_seconds === 'true',
      autoDetectTimezone: settingsData.auto_detect_timezone === 'true',
      datetimeDisplayFormat: settingsData.datetime_display_format || DEFAULT_SETTINGS.datetimeDisplayFormat
    };

    // تحديث الكاش
    cachedSettings = settings;
    lastFetchTime = now;

    return settings;
  } catch (error) {
    console.warn('فشل في جلب إعدادات التاريخ والوقت، استخدام الإعدادات الافتراضية:', error);
    return DEFAULT_SETTINGS;
  }
};

/**
 * مسح كاش الإعدادات لإجبار إعادة التحميل
 */
export const clearDateTimeSettingsCache = (): void => {
  cachedSettings = null;
  lastFetchTime = 0;
};

/**
 * فحص صحة كائن التاريخ
 * @param date كائن التاريخ المراد فحصه
 * @returns true إذا كان التاريخ صحيحاً، false إذا كان غير صحيح
 */
export const isValidDate = (date: Date | string | null | undefined): boolean => {
  if (!date) return false;

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj instanceof Date && !isNaN(dateObj.getTime());
  } catch {
    return false;
  }
};

/**
 * تنسيق آمن للتاريخ مع معالجة الأخطاء
 * @param date التاريخ المراد تنسيقه
 * @param fallback النص البديل في حالة فشل التنسيق
 * @returns التاريخ المنسق أو النص البديل
 */
export const safeFormatDate = (date: Date | string | null | undefined, fallback: string = 'غير محدد'): string => {
  if (!isValidDate(date)) {
    return fallback;
  }

  try {
    return getFormattedDate(date as Date | string);
  } catch (error) {
    console.error('Error in safeFormatDate:', error);
    return fallback;
  }
};

/**
 * الحصول على التاريخ والوقت الحالي بتوقيت طرابلس
 * @returns كائن Date يمثل الوقت الحالي بتوقيت طرابلس
 */
export const getCurrentTripoliDateTime = (): Date => {
  try {
    // الحصول على الوقت الحالي
    const now = new Date();

    // استخدام الوقت الحالي مباشرة
    return now;
  } catch (error) {
    console.error('Error getting current time:', error);
    return new Date();
  }
};

/**
 * تحويل تاريخ إلى تاريخ بتوقيت طرابلس
 * @param date التاريخ المراد تحويله
 * @returns كائن Date يمثل التاريخ المحول إلى توقيت طرابلس
 */
export const convertToTripoliTime = (date: Date | string): Date => {
  if (!date) {
    console.warn('Attempted to convert null/undefined date to Tripoli time');
    return new Date();
  }

  try {
    // إذا كان التاريخ سلسلة، قم بتحويله إلى كائن Date
    const dateObj = typeof date === 'string' ? new Date(date) : new Date(date.getTime());

    // فحص صحة كائن التاريخ
    if (isNaN(dateObj.getTime())) {
      console.warn(`Invalid date object for Tripoli conversion: ${date}`);
      return new Date();
    }

    // بما أن التواريخ يجب أن تُخزن بالفعل بتوقيت طرابلس في قاعدة البيانات،
    // فنستخدم التاريخ كما هو بدون تعديل
    // console.debug(`Using date as is (should be in Tripoli time): ${dateObj.toISOString()}`);
    return dateObj;
  } catch (error) {
    console.error('Error converting to Tripoli time:', error);
    return new Date();
  }
};

/**
 * تنسيق التاريخ والوقت بالصيغة المطلوبة
 * @param date التاريخ المراد تنسيقه
 * @param format صيغة التنسيق (date, time, datetime, يوم، شهر، سنة)
 * @returns سلسلة تمثل التاريخ المنسق
 */
export const formatDateTime = (date: Date | string, format: 'date' | 'time' | 'datetime' | 'day' | 'month' | 'year' = 'datetime'): string => {
  if (!date) {
    console.warn('Attempted to format null/undefined date');
    return '';
  }

  try {
    // إذا كان التاريخ سلسلة، قم بتحويله إلى كائن Date
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // فحص صحة كائن التاريخ
    if (isNaN(dateObj.getTime())) {
      console.warn(`Invalid date object for formatting: ${date}`);
      return '';
    }

    // تحويل التاريخ إلى توقيت طرابلس
    const tripoliDate = convertToTripoliTime(dateObj);

    // فحص صحة التاريخ المحول
    if (isNaN(tripoliDate.getTime())) {
      console.warn(`Invalid converted date for formatting: ${date}`);
      return '';
    }

    // تنسيق التاريخ حسب الصيغة المطلوبة
    let formatted = '';
    switch (format) {
      case 'date':
        formatted = tripoliDate.toLocaleDateString('ar-LY');
        break;
      case 'time':
        formatted = tripoliDate.toLocaleTimeString('ar-LY');
        break;
      case 'datetime':
        formatted = tripoliDate.toLocaleString('ar-LY');
        break;
      case 'day':
        formatted = `${tripoliDate.getDate().toString().padStart(2, '0')}`;
        break;
      case 'month':
        formatted = `${(tripoliDate.getMonth() + 1).toString().padStart(2, '0')}`;
        break;
      case 'year':
        formatted = `${tripoliDate.getFullYear()}`;
        break;
      default:
        formatted = tripoliDate.toLocaleString('ar-LY');
    }

    // console.debug(`Formatted ${tripoliDate.toISOString()} as ${formatted} using format ${format}`);
    return formatted;
  } catch (error) {
    console.error(`Error formatting date ${date} with format ${format}:`, error);
    return '';
  }
};

/**
 * تحويل التاريخ إلى منطقة زمنية محددة
 * @param date التاريخ المراد تحويله
 * @param timezone المنطقة الزمنية المطلوبة
 * @returns كائن Date محول للمنطقة الزمنية المحددة
 */
export const convertToTimezone = (date: Date, timezone: string): Date => {
  try {
    // استخدام Intl.DateTimeFormat للتحويل الدقيق
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });

    const parts = formatter.formatToParts(date);
    const partsObj = parts.reduce((acc, part) => {
      acc[part.type] = part.value;
      return acc;
    }, {} as Record<string, string>);

    return new Date(
      parseInt(partsObj.year),
      parseInt(partsObj.month) - 1,
      parseInt(partsObj.day),
      parseInt(partsObj.hour),
      parseInt(partsObj.minute),
      parseInt(partsObj.second)
    );
  } catch (error) {
    console.error(`Error converting to timezone ${timezone}:`, error);
    return convertToTripoliTime(date);
  }
};

/**
 * تنسيق التاريخ حسب الإعدادات المخصصة
 * @param date التاريخ المراد تنسيقه
 * @param settings إعدادات التنسيق
 * @returns التاريخ منسق حسب الإعدادات
 */
export const formatDateWithSettings = (date: Date, settings: DateTimeSettings): string => {
  try {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();

    let formatted = '';

    switch (settings.dateFormat) {
      case 'dd/MM/yyyy':
        formatted = `${day}${settings.dateSeparator}${month}${settings.dateSeparator}${year}`;
        break;
      case 'MM/dd/yyyy':
        formatted = `${month}${settings.dateSeparator}${day}${settings.dateSeparator}${year}`;
        break;
      case 'yyyy-MM-dd':
        formatted = `${year}${settings.dateSeparator}${month}${settings.dateSeparator}${day}`;
        break;
      case 'dd-MM-yyyy':
        formatted = `${day}${settings.dateSeparator}${month}${settings.dateSeparator}${year}`;
        break;
      case 'dd.MM.yyyy':
        formatted = `${day}${settings.dateSeparator}${month}${settings.dateSeparator}${year}`;
        break;
      case 'arabic':
        const monthNames = [
          'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
          'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        formatted = `${parseInt(day)} ${monthNames[date.getMonth()]} ${year}`;
        break;
      case 'english':
        const englishMonthNames = [
          'January', 'February', 'March', 'April', 'May', 'June',
          'July', 'August', 'September', 'October', 'November', 'December'
        ];
        formatted = `${englishMonthNames[date.getMonth()]} ${parseInt(day)}, ${year}`;
        break;
      default:
        formatted = `${day}${settings.dateSeparator}${month}${settings.dateSeparator}${year}`;
    }

    return formatted;
  } catch (error) {
    console.error('Error formatting date with settings:', error);
    return date.toLocaleDateString(settings.dateLanguage === 'ar' ? 'ar-LY' : 'en-US');
  }
};

/**
 * تنسيق الوقت حسب الإعدادات المخصصة
 * @param date التاريخ المراد تنسيق وقته
 * @param settings إعدادات التنسيق
 * @returns الوقت منسق حسب الإعدادات
 */
export const formatTimeWithSettings = (date: Date, settings: DateTimeSettings): string => {
  try {
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();

    let formatted = '';

    switch (settings.timeFormat) {
      case '24h':
        formatted = `${hours.toString().padStart(2, '0')}${settings.timeSeparator}${minutes.toString().padStart(2, '0')}`;
        if (settings.showSeconds) {
          formatted += `${settings.timeSeparator}${seconds.toString().padStart(2, '0')}`;
        }
        break;
      case '12h':
        const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
        const ampm = hours >= 12 ? 'PM' : 'AM';
        formatted = `${hour12}${settings.timeSeparator}${minutes.toString().padStart(2, '0')}`;
        if (settings.showSeconds) {
          formatted += `${settings.timeSeparator}${seconds.toString().padStart(2, '0')}`;
        }
        formatted += ` ${ampm}`;
        break;
      case '12h_ar':
        const hour12Ar = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
        const ampmAr = hours >= 12 ? 'م' : 'ص';
        formatted = `${hour12Ar}${settings.timeSeparator}${minutes.toString().padStart(2, '0')}`;
        if (settings.showSeconds) {
          formatted += `${settings.timeSeparator}${seconds.toString().padStart(2, '0')}`;
        }
        formatted += ` ${ampmAr}`;
        break;
      default:
        formatted = `${hours.toString().padStart(2, '0')}${settings.timeSeparator}${minutes.toString().padStart(2, '0')}`;
    }

    return formatted;
  } catch (error) {
    console.error('Error formatting time with settings:', error);
    return date.toLocaleTimeString(settings.dateLanguage === 'ar' ? 'ar-LY' : 'en-US');
  }
};

/**
 * تنسيق التاريخ والوقت مع الإعدادات المخصصة (دالة جديدة)
 * @param date التاريخ المراد تنسيقه
 * @param format صيغة التنسيق
 * @param customSettings إعدادات مخصصة (اختيارية)
 * @returns التاريخ والوقت منسق حسب الإعدادات
 */
export const formatDateTimeWithSettings = async (
  date: Date | string,
  format: 'date' | 'time' | 'datetime' | 'day' | 'month' | 'year' = 'datetime',
  customSettings?: DateTimeSettings
): Promise<string> => {
  if (!date) {
    console.warn('Attempted to format null/undefined date');
    return '';
  }

  try {
    // الحصول على الإعدادات
    const settings = customSettings || await fetchDateTimeSettings();

    // إذا كان التاريخ سلسلة، قم بتحويله إلى كائن Date
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // فحص صحة كائن التاريخ
    if (isNaN(dateObj.getTime())) {
      console.warn(`Invalid date object: ${date}`);
      return '';
    }

    // تحويل التاريخ إلى المنطقة الزمنية المحددة
    const convertedDate = convertToTimezone(dateObj, settings.timezone);

    // تنسيق التاريخ حسب الصيغة المطلوبة والإعدادات
    let formatted = '';
    switch (format) {
      case 'date':
        formatted = formatDateWithSettings(convertedDate, settings);
        break;
      case 'time':
        formatted = formatTimeWithSettings(convertedDate, settings);
        break;
      case 'datetime':
        if (settings.datetimeDisplayFormat === 'combined') {
          formatted = `${formatDateWithSettings(convertedDate, settings)} ${formatTimeWithSettings(convertedDate, settings)}`;
        } else {
          formatted = `${formatDateWithSettings(convertedDate, settings)}\n${formatTimeWithSettings(convertedDate, settings)}`;
        }
        break;
      case 'day':
        formatted = `${convertedDate.getDate().toString().padStart(2, '0')}`;
        break;
      case 'month':
        formatted = `${(convertedDate.getMonth() + 1).toString().padStart(2, '0')}`;
        break;
      case 'year':
        formatted = `${convertedDate.getFullYear()}`;
        break;
      default:
        formatted = formatDateWithSettings(convertedDate, settings);
    }

    return formatted;
  } catch (error) {
    console.error(`Error formatting date ${date}:`, error);
    return '';
  }
};

/**
 * الحصول على ساعة من تاريخ بتنسيق 24 ساعة
 * @param date التاريخ المراد استخراج الساعة منه
 * @returns سلسلة تمثل الساعة بتنسيق 24 ساعة (مثل "14:00")
 */
export const getHourFromDate = (date: Date | string): string => {
  if (!date) {
    console.warn('Attempted to get hour from null/undefined date');
    return '00:00';
  }

  try {
    // إذا كان التاريخ سلسلة، قم بتحويله إلى كائن Date
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // فحص صحة كائن التاريخ
    if (isNaN(dateObj.getTime())) {
      console.warn(`Invalid date object for hour extraction: ${date}`);
      return '00:00';
    }

    // تحويل التاريخ إلى توقيت طرابلس
    const tripoliDate = convertToTripoliTime(dateObj);

    // فحص صحة التاريخ المحول
    if (isNaN(tripoliDate.getTime())) {
      console.warn(`Invalid converted date for hour extraction: ${date}`);
      return '00:00';
    }

    // استخراج الساعة وتنسيقها
    const hour = tripoliDate.getHours();
    const formatted = `${hour.toString().padStart(2, '0')}:00`;

    // console.debug(`Extracted hour ${formatted} from ${tripoliDate.toISOString()}`);
    return formatted;
  } catch (error) {
    console.error(`Error extracting hour from date ${date}:`, error);
    return '00:00';
  }
};

/**
 * الحصول على تاريخ بتنسيق YYYY-MM-DD
 * @param date التاريخ المراد تنسيقه
 * @returns سلسلة تمثل التاريخ بتنسيق YYYY-MM-DD
 */
export const getFormattedDate = (date: Date | string): string => {
  if (!date) {
    console.warn('Attempted to format null/undefined date');
    return '';
  }

  try {
    // إذا كان التاريخ سلسلة، قم بتحويله إلى كائن Date
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // فحص صحة كائن التاريخ
    if (isNaN(dateObj.getTime())) {
      console.warn(`Invalid date object: ${date}`);
      return '';
    }

    // تحويل التاريخ إلى توقيت طرابلس
    const tripoliDate = convertToTripoliTime(dateObj);

    // فحص صحة التاريخ المحول
    if (isNaN(tripoliDate.getTime())) {
      console.warn(`Invalid converted date: ${date}`);
      return '';
    }

    // تنسيق التاريخ
    const year = tripoliDate.getFullYear();
    const month = (tripoliDate.getMonth() + 1).toString().padStart(2, '0');
    const day = tripoliDate.getDate().toString().padStart(2, '0');

    const formatted = `${year}-${month}-${day}`;

    // console.debug(`Formatted date ${tripoliDate.toISOString()} as ${formatted}`);
    return formatted;
  } catch (error) {
    console.error(`Error formatting date ${date}:`, error);
    return '';
  }
};

/**
 * الحصول على الشهر والسنة بتنسيق YYYY-MM
 * @param date التاريخ المراد تنسيقه
 * @returns سلسلة تمثل الشهر والسنة بتنسيق YYYY-MM
 */
export const getYearMonth = (date: Date | string): string => {
  if (!date) {
    console.warn('Attempted to format null/undefined date');
    return '';
  }

  try {
    // إذا كان التاريخ سلسلة، قم بتحويله إلى كائن Date
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // فحص صحة كائن التاريخ
    if (isNaN(dateObj.getTime())) {
      console.warn(`Invalid date object for year-month: ${date}`);
      return '';
    }

    // تحويل التاريخ إلى توقيت طرابلس
    const tripoliDate = convertToTripoliTime(dateObj);

    // فحص صحة التاريخ المحول
    if (isNaN(tripoliDate.getTime())) {
      console.warn(`Invalid converted date for year-month: ${date}`);
      return '';
    }

    // تنسيق التاريخ
    const year = tripoliDate.getFullYear();
    const month = (tripoliDate.getMonth() + 1).toString().padStart(2, '0');

    const formatted = `${year}-${month}`;

    // console.debug(`Formatted year-month ${tripoliDate.toISOString()} as ${formatted}`);
    return formatted;
  } catch (error) {
    console.error(`Error formatting year-month for date ${date}:`, error);
    return '';
  }
};

/**
 * الحصول على اسم الشهر بالعربية
 * @param month رقم الشهر (1-12)
 * @returns اسم الشهر بالعربية
 */
export const getArabicMonthName = (month: number): string => {
  const arabicMonths = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  // التأكد من أن الشهر في النطاق الصحيح (1-12)
  if (month < 1 || month > 12) {
    console.warn(`Invalid month number: ${month}, must be between 1 and 12`);
    return '';
  }

  const monthName = arabicMonths[month - 1];
  console.debug(`Month ${month} in Arabic: ${monthName}`);
  return monthName;
};

/**
 * الحصول على اختصار الشهر بالعربية
 * @param month رقم الشهر (1-12)
 * @returns اختصار الشهر بالعربية
 */
export const getArabicMonthAbbr = (month: number): string => {
  const arabicMonthAbbr = ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'];

  // التأكد من أن الشهر في النطاق الصحيح (1-12)
  if (month < 1 || month > 12) {
    console.warn(`Invalid month number: ${month}, must be between 1 and 12`);
    return '';
  }

  const monthAbbr = arabicMonthAbbr[month - 1];
  console.debug(`Month ${month} abbreviation in Arabic: ${monthAbbr}`);
  return monthAbbr;
};

/**
 * إنشاء مصفوفة من التواريخ للأيام السابقة
 * @param days عدد الأيام السابقة
 * @returns مصفوفة من التواريخ بتنسيق YYYY-MM-DD مرتبة من الأقدم إلى الأحدث
 */
export const getPreviousDays = (days: number): string[] => {
  if (days <= 0) {
    console.warn(`Invalid days count: ${days}, must be positive`);
    return [];
  }

  const result: string[] = [];
  const today = getCurrentTripoliDateTime();

  // console.debug(`Generating ${days} previous days from ${today.toISOString()}`);

  // Generate days in chronological order (oldest to newest)
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const formatted = getFormattedDate(date);
    if (formatted) { // فقط إضافة التواريخ الصحيحة
      result.push(formatted);
      // console.debug(`Added day ${days-i}/${days}: ${formatted}`);
    }
  }

  return result;
};

/**
 * إنشاء مصفوفة من الشهور السابقة
 * @param months عدد الشهور السابقة
 * @returns مصفوفة من الشهور بتنسيق YYYY-MM مرتبة من الأقدم إلى الأحدث
 */
export const getPreviousMonths = (months: number): string[] => {
  if (months <= 0) {
    console.warn(`Invalid months count: ${months}, must be positive`);
    return [];
  }

  const result: string[] = [];
  const today = getCurrentTripoliDateTime();

  // console.debug(`Generating ${months} previous months from ${today.toISOString()}`);

  // Generate months in chronological order (oldest to newest)
  for (let i = months - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setMonth(date.getMonth() - i);
    const formatted = getYearMonth(date);
    if (formatted) { // فقط إضافة الشهور الصحيحة
      result.push(formatted);
      // console.debug(`Added month ${months-i}/${months}: ${formatted}`);
    }
  }

  return result;
};

/**
 * تنسيق فترة كفاءة التحصيل للعرض
 * @param period الفترة من الخادم (مثل 2025-07 أو 2025-W28)
 * @param periodType نوع الفترة (day, week, month, year) - اختياري
 * @returns الفترة منسقة للعرض باللغة العربية
 */
export const formatCollectionPeriod = (period: string, periodType?: string): string => {
  if (!period) {
    console.warn('Attempted to format empty period');
    return '';
  }

  try {
    // إذا تم تمرير نوع الفترة، استخدم نفس منطق اتجاهات المديونية
    if (periodType) {
      return formatDebtTrendPeriod(period, periodType);
    }

    // الكشف التلقائي عن نوع الفترة (للتوافق مع الكود القديم)
    // فترة شهرية (YYYY-MM)
    if (/^\d{4}-\d{2}$/.test(period)) {
      const [year, month] = period.split('-');
      const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];
      const monthIndex = parseInt(month) - 1;
      return `${monthNames[monthIndex]} ${year}`;
    }

    // فترة أسبوعية (YYYY-WNN)
    if (/^\d{4}-W\d{2}$/.test(period)) {
      const [year, weekPart] = period.split('-W');
      const weekNum = parseInt(weekPart);
      return `الأسبوع ${weekNum} من ${year}`;
    }

    // فترة يومية (YYYY-MM-DD)
    if (/^\d{4}-\d{2}-\d{2}$/.test(period)) {
      return formatShortDate(period); // استخدام التنسيق المختصر
    }

    // فترة سنوية (YYYY)
    if (/^\d{4}$/.test(period)) {
      return `سنة ${period}`;
    }

    // إذا لم يتطابق مع أي نمط، أرجع كما هو
    return period;
  } catch (error) {
    console.error(`Error formatting collection period ${period}:`, error);
    return period;
  }
};

/**
 * تنسيق التاريخ بشكل مختصر للمخططات
 * @param dateStr التاريخ بصيغة YYYY-MM-DD
 * @returns التاريخ منسق بشكل مختصر (مثل: 03-يوليو)
 */
export const formatShortDate = (dateStr: string): string => {
  if (!dateStr) {
    console.warn('Attempted to format empty date string');
    return '';
  }

  try {
    const date = new Date(dateStr);
    const day = date.getDate().toString().padStart(2, '0');
    const monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    const month = monthNames[date.getMonth()];

    return `${day}-${month}`;
  } catch (error) {
    console.error(`Error formatting short date ${dateStr}:`, error);
    return dateStr;
  }
};

/**
 * تنسيق فترة اتجاهات المديونية للعرض
 * @param period الفترة من الخادم
 * @param periodType نوع الفترة (day, week, month, year)
 * @returns الفترة منسقة للعرض
 */
export const formatDebtTrendPeriod = (period: string, periodType: string): string => {
  if (!period) {
    console.warn('Attempted to format empty debt trend period');
    return '';
  }

  try {
    switch (periodType) {
      case 'day':
        return formatShortDate(period);

      case 'week':
        // فترة أسبوعية (YYYY-WNN)
        if (/^\d{4}-W\d{2}$/.test(period)) {
          const [year, weekPart] = period.split('-W');
          const weekNum = parseInt(weekPart);
          return `أ${weekNum}`;
        }
        return period;

      case 'month':
        // فترة شهرية (YYYY-MM)
        if (/^\d{4}-\d{2}$/.test(period)) {
          const [year, month] = period.split('-');
          const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
          ];
          const monthIndex = parseInt(month) - 1;
          return `${monthNames[monthIndex].substring(0, 3)} ${year}`;
        }
        return period;

      case 'year':
        return period;

      default:
        return period;
    }
  } catch (error) {
    console.error(`Error formatting debt trend period ${period}:`, error);
    return period;
  }
};

/**
 * تحليل سلسلة تاريخ إلى كائن Date
 * @param dateStr سلسلة التاريخ المراد تحليلها
 * @param format صيغة التاريخ (YYYY-MM-DD أو YYYY-MM أو HH:MM)
 * @returns كائن Date يمثل التاريخ المحلل
 */
export const parseDateString = (dateStr: string, format: 'date' | 'month' | 'hour' = 'date'): Date => {
  if (!dateStr) {
    console.warn('Attempted to parse empty date string');
    return new Date();
  }

  try {
    let date: Date;

    switch (format) {
      case 'date':
        // Format: YYYY-MM-DD
        if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
          const [year, month, day] = dateStr.split('-').map(Number);
          date = new Date(year, month - 1, day);
        } else {
          throw new Error(`Invalid date format: ${dateStr}, expected YYYY-MM-DD`);
        }
        break;

      case 'month':
        // Format: YYYY-MM
        if (dateStr.match(/^\d{4}-\d{2}$/)) {
          const [year, month] = dateStr.split('-').map(Number);
          date = new Date(year, month - 1, 1);
        } else {
          throw new Error(`Invalid month format: ${dateStr}, expected YYYY-MM`);
        }
        break;

      case 'hour':
        // Format: HH:MM
        if (dateStr.match(/^\d{2}:\d{2}$/)) {
          const [hour, minute] = dateStr.split(':').map(Number);
          date = new Date();
          date.setHours(hour, minute, 0, 0);
        } else {
          throw new Error(`Invalid hour format: ${dateStr}, expected HH:MM`);
        }
        break;

      default:
        throw new Error(`Unsupported format: ${format}`);
    }

    // فحص صحة التاريخ المحلل
    if (isNaN(date.getTime())) {
      console.warn(`Parsed date is invalid: ${dateStr}`);
      return new Date();
    }

    // console.debug(`Parsed ${dateStr} as ${date.toISOString()} using format ${format}`);
    return date;
  } catch (error) {
    console.error(`Failed to parse date string ${dateStr} with format ${format}:`, error);
    return new Date();
  }
};
